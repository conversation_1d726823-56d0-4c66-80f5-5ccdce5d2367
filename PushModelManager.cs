using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Windows.Forms;

namespace BIS推送配置
{
    /// <summary>
    /// 推送机型表专用管理类
    /// 提供针对推送机型表的专门CRUD操作和错误处理
    /// </summary>
    public class PushModelManager
    {
        private readonly string connectionString;
        private const string TABLE_NAME = "推送机型";
        
        public PushModelManager()
        {
            connectionString = ConfigurationManager.ConnectionStrings["TEDatabase"].ConnectionString;
        }
        
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public bool TestConnection(out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"数据库连接失败：{ex.Message}";
                return false;
            }
        }
        
        /// <summary>
        /// 检查推送机型表是否存在
        /// </summary>
        public bool CheckTableExists(out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@tableName", TABLE_NAME);
                        int count = (int)cmd.ExecuteScalar();
                        
                        if (count == 0)
                        {
                            errorMessage = $"表 [{TABLE_NAME}] 不存在";
                            return false;
                        }
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"检查表存在性失败：{ex.Message}";
                return false;
            }
        }
        
        /// <summary>
        /// 获取推送机型表的所有数据
        /// </summary>
        public DataTable GetAllData(out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = $"SELECT * FROM [{TABLE_NAME}] ORDER BY 编号";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"查询数据失败：{ex.Message}\n详细信息：{ex}";
                return null;
            }
        }
        
        /// <summary>
        /// 根据机型名称查询数据
        /// </summary>
        public DataTable SearchByModelName(string modelName, out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = $"SELECT * FROM [{TABLE_NAME}] WHERE [机型名称] LIKE @modelName ORDER BY 编号";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@modelName", $"%{modelName}%");
                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"搜索数据失败：{ex.Message}\n详细信息：{ex}";
                return null;
            }
        }
        
        /// <summary>
        /// 添加新的推送机型记录
        /// </summary>
        public bool AddModel(string modelName, out string errorMessage, out int newId)
        {
            errorMessage = string.Empty;
            newId = 0;
            
            if (string.IsNullOrWhiteSpace(modelName))
            {
                errorMessage = "机型名称不能为空";
                return false;
            }
            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    
                    // 检查是否已存在相同机型名称
                    string checkQuery = $"SELECT COUNT(*) FROM [{TABLE_NAME}] WHERE [机型名称] = @modelName";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                    {
                        checkCmd.Parameters.AddWithValue("@modelName", modelName);
                        int existCount = (int)checkCmd.ExecuteScalar();
                        if (existCount > 0)
                        {
                            errorMessage = $"机型名称 '{modelName}' 已存在";
                            return false;
                        }
                    }
                    
                    // 插入新记录
                    string insertQuery = $@"
                        INSERT INTO [{TABLE_NAME}] ([机型名称], [创建人]) 
                        VALUES (@modelName, @creator);
                        SELECT SCOPE_IDENTITY();";
                    
                    using (SqlCommand cmd = new SqlCommand(insertQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@modelName", modelName.Trim());
                        cmd.Parameters.AddWithValue("@creator", "System");
                        
                        object result = cmd.ExecuteScalar();
                        newId = Convert.ToInt32(result);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"添加记录失败：{ex.Message}\n详细信息：{ex}";
                return false;
            }
        }
        
        /// <summary>
        /// 更新推送机型记录
        /// </summary>
        public bool UpdateModel(int id, string modelName, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            if (string.IsNullOrWhiteSpace(modelName))
            {
                errorMessage = "机型名称不能为空";
                return false;
            }
            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    
                    // 检查记录是否存在
                    string checkQuery = $"SELECT COUNT(*) FROM [{TABLE_NAME}] WHERE [编号] = @id";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                    {
                        checkCmd.Parameters.AddWithValue("@id", id);
                        int existCount = (int)checkCmd.ExecuteScalar();
                        if (existCount == 0)
                        {
                            errorMessage = $"编号为 {id} 的记录不存在";
                            return false;
                        }
                    }
                    
                    // 检查是否有其他记录使用相同机型名称
                    string duplicateQuery = $"SELECT COUNT(*) FROM [{TABLE_NAME}] WHERE [机型名称] = @modelName AND [编号] != @id";
                    using (SqlCommand duplicateCmd = new SqlCommand(duplicateQuery, conn))
                    {
                        duplicateCmd.Parameters.AddWithValue("@modelName", modelName);
                        duplicateCmd.Parameters.AddWithValue("@id", id);
                        int duplicateCount = (int)duplicateCmd.ExecuteScalar();
                        if (duplicateCount > 0)
                        {
                            errorMessage = $"机型名称 '{modelName}' 已被其他记录使用";
                            return false;
                        }
                    }
                    
                    // 更新记录
                    string updateQuery = $@"
                        UPDATE [{TABLE_NAME}] 
                        SET [机型名称] = @modelName, [更新人] = @updater, [更新时间] = @updateTime 
                        WHERE [编号] = @id";
                    
                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@modelName", modelName.Trim());
                        cmd.Parameters.AddWithValue("@updater", "System");
                        cmd.Parameters.AddWithValue("@updateTime", DateTime.Now);
                        cmd.Parameters.AddWithValue("@id", id);
                        
                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"更新记录失败：{ex.Message}\n详细信息：{ex}";
                return false;
            }
        }
        
        /// <summary>
        /// 删除推送机型记录
        /// </summary>
        public bool DeleteModel(int id, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    
                    // 检查记录是否存在
                    string checkQuery = $"SELECT [机型名称] FROM [{TABLE_NAME}] WHERE [编号] = @id";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                    {
                        checkCmd.Parameters.AddWithValue("@id", id);
                        object result = checkCmd.ExecuteScalar();
                        if (result == null)
                        {
                            errorMessage = $"编号为 {id} 的记录不存在";
                            return false;
                        }
                    }
                    
                    // 删除记录
                    string deleteQuery = $"DELETE FROM [{TABLE_NAME}] WHERE [编号] = @id";
                    using (SqlCommand cmd = new SqlCommand(deleteQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@id", id);
                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"删除记录失败：{ex.Message}\n详细信息：{ex}";
                return false;
            }
        }
        
        /// <summary>
        /// 获取表结构信息
        /// </summary>
        public DataTable GetTableSchema(out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = @"
                        SELECT
                            c.COLUMN_NAME,
                            c.DATA_TYPE,
                            c.CHARACTER_MAXIMUM_LENGTH,
                            c.IS_NULLABLE,
                            c.COLUMN_DEFAULT,
                            c.ORDINAL_POSITION
                        FROM INFORMATION_SCHEMA.COLUMNS c
                        WHERE c.TABLE_NAME = @tableName
                        ORDER BY c.ORDINAL_POSITION";
                    
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@tableName", TABLE_NAME);
                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable schema = new DataTable();
                        adapter.Fill(schema);
                        return schema;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"获取表结构失败：{ex.Message}";
                return null;
            }
        }
    }
}
