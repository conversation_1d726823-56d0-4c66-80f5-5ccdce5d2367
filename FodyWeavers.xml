<?xml version="1.0" encoding="utf-8"?>
<Weavers xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="FodyWeavers.xsd">
  <Costura>
    <!-- 包含所有依赖项 -->
    <IncludeAssemblies>
      AntdUI
      IrisSkin4
    </IncludeAssemblies>
    <!-- 预加载程序集，确保皮肤引擎优先加载 -->
    <PreloadOrder>
      IrisSkin4
      AntdUI
    </PreloadOrder>
    <!-- 创建临时程序集 -->
    <CreateTemporaryAssemblies>true</CreateTemporaryAssemblies>
    <!-- 禁用压缩以避免兼容性问题 -->
    <DisableCompression>true</DisableCompression>
    <!-- 忽略卫星程序集 -->
    <IgnoreSatelliteAssemblies>false</IgnoreSatelliteAssemblies>
  </Costura>
</Weavers>
