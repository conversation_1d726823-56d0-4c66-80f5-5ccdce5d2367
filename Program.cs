﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Reflection;
using System.IO;

namespace BIS推送配置
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 提取嵌入的依赖项
                ExtractEmbeddedDependencies();

                // 添加程序集解析事件处理
                AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new Form1());
            }
            catch (Exception ex)
            {
                // 显示详细的错误信息
                MessageBox.Show($"程序启动失败：\n{ex.Message}\n\n详细信息：\n{ex.ToString()}",
                    "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 提取嵌入的依赖项到临时目录
        /// </summary>
        private static void ExtractEmbeddedDependencies()
        {
            try
            {
                Assembly executingAssembly = Assembly.GetExecutingAssembly();
                string tempDir = Path.GetTempPath();
                string appTempDir = Path.Combine(tempDir, "BIS推送配置_Dependencies");

                if (!Directory.Exists(appTempDir))
                {
                    Directory.CreateDirectory(appTempDir);
                }

                // 提取依赖的DLL文件
                string[] dependencies = { "AntdUI.dll", "IrisSkin4.dll" };

                foreach (string dependency in dependencies)
                {
                    string targetPath = Path.Combine(appTempDir, dependency);
                    if (!File.Exists(targetPath))
                    {
                        // 尝试从嵌入资源中提取
                        string resourceName = $"BIS推送配置.{dependency}";
                        using (Stream stream = executingAssembly.GetManifestResourceStream(resourceName))
                        {
                            if (stream != null)
                            {
                                using (FileStream fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write))
                                {
                                    stream.CopyTo(fileStream);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 提取失败不影响程序启动，依赖Costura.Fody处理
                System.Diagnostics.Debug.WriteLine($"提取依赖项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 程序集解析事件处理
        /// </summary>
        private static Assembly CurrentDomain_AssemblyResolve(object sender, ResolveEventArgs args)
        {
            try
            {
                string assemblyName = new AssemblyName(args.Name).Name;

                // 首先尝试从临时目录加载
                string tempDir = Path.GetTempPath();
                string appTempDir = Path.Combine(tempDir, "BIS推送配置_Dependencies");
                string assemblyPath = Path.Combine(appTempDir, assemblyName + ".dll");

                if (File.Exists(assemblyPath))
                {
                    return Assembly.LoadFrom(assemblyPath);
                }

                // 尝试从当前程序集的嵌入资源中加载（Costura.Fody方式）
                Assembly executingAssembly = Assembly.GetExecutingAssembly();
                string resourceName = $"costura.{assemblyName.ToLowerInvariant()}.dll";

                using (Stream stream = executingAssembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        byte[] assemblyData = new byte[stream.Length];
                        stream.Read(assemblyData, 0, assemblyData.Length);
                        return Assembly.Load(assemblyData);
                    }
                }

                // 尝试从应用程序目录加载
                string appDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                string localAssemblyPath = Path.Combine(appDir, assemblyName + ".dll");
                if (File.Exists(localAssemblyPath))
                {
                    return Assembly.LoadFrom(localAssemblyPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载程序集 {args.Name} 失败：{ex.Message}");
            }

            return null;
        }
    }
}
