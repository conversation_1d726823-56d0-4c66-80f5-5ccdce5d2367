﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BIS推送配置
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new Form1());
            }
            catch (Exception ex)
            {
                // 显示详细的错误信息
                MessageBox.Show($"程序启动失败：\n{ex.Message}\n\n详细信息：\n{ex.ToString()}",
                    "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
