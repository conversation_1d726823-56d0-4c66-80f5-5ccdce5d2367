@echo off
echo 正在合并程序集到单个exe文件...

REM 设置路径
set ILMERGE_PATH=..\packages\ILMerge.3.0.41\tools\net452\ILMerge.exe
set OUTPUT_DIR=bin\Release
set MERGED_EXE=BIS推送配置_Merged.exe

REM 检查ILMerge是否存在
if not exist "%ILMERGE_PATH%" (
    echo 错误：找不到ILMerge.exe，请确保已安装ILMerge NuGet包
    pause
    exit /b 1
)

REM 检查输出目录是否存在
if not exist "%OUTPUT_DIR%" (
    echo 错误：找不到Release输出目录，请先编译项目
    pause
    exit /b 1
)

REM 使用ILMerge合并程序集
echo 正在使用ILMerge合并程序集...
"%ILMERGE_PATH%" /target:winexe /out:"%OUTPUT_DIR%\%MERGED_EXE%" ^
    "%OUTPUT_DIR%\BIS推送配置.exe" ^
    "%OUTPUT_DIR%\AntdUI.dll" ^
    "%OUTPUT_DIR%\IrisSkin4.dll" ^
    /targetplatform:v4,C:\Windows\Microsoft.NET\Framework64\v4.0.30319

if %ERRORLEVEL% EQU 0 (
    echo 成功！合并后的exe文件：%OUTPUT_DIR%\%MERGED_EXE%
    
    REM 复制皮肤文件到输出目录
    copy "%OUTPUT_DIR%\RealOne.ssk" "%OUTPUT_DIR%\" >nul 2>&1
    copy "%OUTPUT_DIR%\BIS推送配置.exe.config" "%OUTPUT_DIR%\" >nul 2>&1
    
    echo 已复制配置文件和皮肤文件
    echo.
    echo 单个exe文件已准备就绪：%OUTPUT_DIR%\%MERGED_EXE%
) else (
    echo 错误：ILMerge合并失败
)

pause
