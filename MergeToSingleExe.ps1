Write-Host "正在合并程序集到单个exe文件..." -ForegroundColor Green

# 设置路径
$ILMERGE_PATH = "..\packages\ILMerge.3.0.41\tools\net452\ILMerge.exe"
$OUTPUT_DIR = "bin\Release"
$MERGED_EXE = "BIS推送配置_Merged.exe"

# 检查ILMerge是否存在
if (-not (Test-Path $ILMERGE_PATH)) {
    Write-Host "错误：找不到ILMerge.exe，请确保已安装ILMerge NuGet包" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查输出目录是否存在
if (-not (Test-Path $OUTPUT_DIR)) {
    Write-Host "错误：找不到Release输出目录，请先编译项目" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 使用ILMerge合并程序集
Write-Host "正在使用ILMerge合并程序集..." -ForegroundColor Yellow

$arguments = @(
    "/target:winexe",
    "/out:$OUTPUT_DIR\$MERGED_EXE",
    "$OUTPUT_DIR\BIS推送配置.exe",
    "$OUTPUT_DIR\AntdUI.dll",
    "$OUTPUT_DIR\IrisSkin4.dll",
    "/targetplatform:v4,C:\Windows\Microsoft.NET\Framework64\v4.0.30319"
)

try {
    & $ILMERGE_PATH $arguments
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "成功！合并后的exe文件：$OUTPUT_DIR\$MERGED_EXE" -ForegroundColor Green
        
        # 复制皮肤文件和配置文件
        Copy-Item "$OUTPUT_DIR\RealOne.ssk" "$OUTPUT_DIR\" -Force -ErrorAction SilentlyContinue
        Copy-Item "$OUTPUT_DIR\BIS推送配置.exe.config" "$OUTPUT_DIR\" -Force -ErrorAction SilentlyContinue
        
        Write-Host "已复制配置文件和皮肤文件" -ForegroundColor Green
        Write-Host ""
        Write-Host "单个exe文件已准备就绪：$OUTPUT_DIR\$MERGED_EXE" -ForegroundColor Cyan
    } else {
        Write-Host "错误：ILMerge合并失败，退出代码：$LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "错误：执行ILMerge时发生异常：$($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "按任意键退出"
