﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Configuration;

namespace BIS推送配置
{
    //public partial class Form1 : Form
    public partial class Form1 : AntdUI.Window
    {
        private string connectionString;
        private string currentTableName;
        private DataTable currentTableSchema;
        private List<Control> inputControls;
        private Dictionary<string, string> tableColumns;
        private List<string> testTypes = new List<string> { "正常品", "维修品", "返工品", "返工维修品" };
        private CheckedListBox testTypeCheckedListBox;
        private PushModelManager pushModelManager;

        public Form1()
        {
            InitializeComponent();
            connectionString = ConfigurationManager.ConnectionStrings["TEDatabase"].ConnectionString;
            inputControls = new List<Control>();
            tableColumns = new Dictionary<string, string>();
            pushModelManager = new PushModelManager();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            LoadTableList();
            InitializeSkinEngineSafely();
        }

        /// <summary>
        /// 安全地初始化皮肤引擎
        /// </summary>
        private void InitializeSkinEngineSafely()
        {
            try
            {
                this.skinEngine1.SkinFile = "RealOne.ssk"; //DiamondBlue.ssk
                this.skinEngine1.Active = true;
            }
            catch (Exception ex)
            {
                // 如果皮肤引擎初始化失败，禁用它并继续运行
                try
                {
                    this.skinEngine1.Active = false;
                }
                catch
                {
                    // 忽略禁用皮肤引擎时的错误
                }
                AntdUI.Message.error(this, "提示：皮肤引擎初始化失败！", null, 2);
                
                   
            }
        }

        private void LoadTableList()
        {
            try
            {
                treeViewTables.Nodes.Clear();
                TreeNode rootNode = new TreeNode("数据库表");
                treeViewTables.Nodes.Add(rootNode);

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string tableName = reader["TABLE_NAME"].ToString();
                                TreeNode tableNode = new TreeNode(tableName);
                                tableNode.Tag = tableName;
                                rootNode.Nodes.Add(tableNode);
                            }
                        }
                    }
                }
                rootNode.Expand();
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：加载表列表失败！", null, 2);
                
            }
        }

        private void treeViewTables_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag != null)
            {
                string tableName = e.Node.Tag.ToString();
                LoadTableData(tableName);
            }
        }

        private void LoadTableData(string tableName)
        {
            try
            {
                currentTableName = tableName;
                lblCurrentTable.Text = $"当前表：{tableName}";

                // 特殊处理推送机型表
                if (tableName == "推送机型")
                {
                    // 测试连接和表存在性
                    string errorMessage;
                    if (!pushModelManager.TestConnection(out errorMessage))
                    {
                        AntdUI.Message.error(this, "提示：推送机型表连接测试失败！", null, 2);
                       
                        return;
                    }

                    if (!pushModelManager.CheckTableExists(out errorMessage))
                    {
                        AntdUI.Message.error(this, "提示：推送机型表检查失败！", null, 2);
                       
                        return;
                    }
                }

                // 获取表结构
                LoadTableSchema(tableName);

                // 生成输入控件
                GenerateInputControls();

                // 加载数据
                RefreshData();
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：加载表数据失败！", null, 2);

               
            }
        }

        private void LoadTableSchema(string tableName)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                string query = @"
                    SELECT
                        c.COLUMN_NAME,
                        c.DATA_TYPE,
                        c.CHARACTER_MAXIMUM_LENGTH,
                        c.NUMERIC_PRECISION,
                        c.NUMERIC_SCALE,
                        c.IS_NULLABLE,
                        c.COLUMN_DEFAULT,
                        c.ORDINAL_POSITION
                    FROM INFORMATION_SCHEMA.COLUMNS c
                    WHERE c.TABLE_NAME = @tableName
                    ORDER BY c.ORDINAL_POSITION";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@tableName", tableName);
                    SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                    currentTableSchema = new DataTable();
                    adapter.Fill(currentTableSchema);
                }
            }
        }

        private void GenerateInputControls()
        {
            // 清除现有控件
            gbInputs.Controls.Clear();
            inputControls.Clear();
            tableColumns.Clear();

            int yPosition = 25;
            int labelWidth = 100;
            int controlWidth = 200;
            int spacing = 35;

            foreach (DataRow row in currentTableSchema.Rows)
            {
                string columnName = row["COLUMN_NAME"].ToString();
                string dataType = row["DATA_TYPE"].ToString();
                bool isNullable = row["IS_NULLABLE"].ToString() == "YES";

                tableColumns[columnName] = dataType;

                // 跳过自动处理的字段
                if (columnName == "编号" || columnName == "创建人" || columnName == "创建时间" || columnName == "更新时间" || columnName == "更新人")
                {
                    // 为这些字段添加占位控件，但不显示
                    Control placeholderControl = new TextBox();
                    placeholderControl.Visible = false;
                    inputControls.Add(placeholderControl);
                    continue;
                }

                // 特殊处理测试类型字段
                if (columnName == "测试类型")
                {
                    // 创建测试类型标签
                    Label testTypeLabel = new Label();
                    testTypeLabel.Text = columnName + "*";
                    testTypeLabel.Location = new Point(10, yPosition);
                    testTypeLabel.Size = new Size(labelWidth, 20);
                    testTypeLabel.ForeColor = Color.Red;
                    gbInputs.Controls.Add(testTypeLabel);

                    // 创建可多选的下拉列表框
                    testTypeCheckedListBox = new CheckedListBox();
                    testTypeCheckedListBox.Location = new Point(labelWidth + 20, yPosition - 3);
                    testTypeCheckedListBox.Size = new Size(controlWidth, 80);
                    testTypeCheckedListBox.CheckOnClick = true;

                    // 添加测试类型选项
                    foreach (string testType in testTypes)
                    {
                        testTypeCheckedListBox.Items.Add(testType);
                    }

                    gbInputs.Controls.Add(testTypeCheckedListBox);
                    inputControls.Add(testTypeCheckedListBox);
                    yPosition += 90; // 为下拉列表框留更多空间
                    continue;
                }

                // 创建标签
                Label label = new Label();
                label.Text = columnName + (isNullable ? "" : "*");
                label.Location = new Point(10, yPosition);
                label.Size = new Size(labelWidth, 20);
                label.ForeColor = isNullable ? Color.Black : Color.Red;
                gbInputs.Controls.Add(label);

                // 创建输入控件
                Control inputControl = CreateInputControl(dataType, columnName);
                inputControl.Name = "txt" + columnName;
                inputControl.Location = new Point(labelWidth + 20, yPosition - 3);
                inputControl.Size = new Size(controlWidth, 23);
                gbInputs.Controls.Add(inputControl);
                inputControls.Add(inputControl);

                yPosition += spacing;
            }
        }

        private Control CreateInputControl(string dataType, string columnName)
        {
            switch (dataType.ToLower())
            {
                case "bit":
                    return new CheckBox();
                case "datetime":
                case "datetime2":
                case "date":
                    return new DateTimePicker();
                case "int":
                case "bigint":
                case "smallint":
                case "tinyint":
                case "decimal":
                case "numeric":
                case "float":
                case "real":
                case "money":
                case "smallmoney":
                    NumericUpDown numericUpDown = new NumericUpDown();
                    numericUpDown.Maximum = decimal.MaxValue;
                    numericUpDown.Minimum = decimal.MinValue;
                    return numericUpDown;
                default:
                    return new TextBox();
            }
        }

        private void RefreshData()
        {
            SafeRefreshData();
        }

        /// <summary>
        /// 安全的数据刷新方法，处理皮肤引擎兼容性问题
        /// </summary>
        private void SafeRefreshData()
        {
            if (string.IsNullOrEmpty(currentTableName))
                return;

            try
            {
                // 特殊处理推送机型表
                if (currentTableName == "推送机型")
                {
                    string errorMessage;
                    DataTable dataTable = pushModelManager.GetAllData(out errorMessage);
                    if (dataTable != null)
                    {
                        // 限制文本长度以避免皮肤引擎问题
                        LimitDataTableTextLength(dataTable);
                        dgvData.DataSource = dataTable;
                    }
                    else
                    {
                        AntdUI.Message.error(this, "提示：刷新推送机型数据失败！", null, 2);
                      
                    }
                    return;
                }

                // 其他表的正常处理
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    // 所有表都显示原始数据，每个测试类型一行
                    string query = $"SELECT * FROM [{currentTableName}] ORDER BY 编号";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);

                        // 限制文本长度以避免皮肤引擎问题
                        LimitDataTableTextLength(dataTable);
                        dgvData.DataSource = dataTable;
                    }
                }
            }
            catch (ArgumentOutOfRangeException ex) when (ex.Message.Contains("容量超出了最大容量") || ex.StackTrace.Contains("IrisSkin"))
            {
                // 皮肤引擎错误，尝试禁用皮肤引擎后重试
                try
                {
                    bool skinEngineWasActive = skinEngine1.Active;
                    skinEngine1.Active = false;

                    // 重新尝试刷新数据
                    RefreshDataWithoutSkin();

                    AntdUI.Message.success(this, "提示：刷新数据成功！", null, 2);
                }
                catch (Exception retryEx)
                {
                    AntdUI.Message.error(this, "提示：刷新数据失败！", null, 2);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：刷新数据失败！", null, 2);
            }
        }

        /// <summary>
        /// 限制DataTable中文本字段的长度，避免皮肤引擎问题
        /// </summary>
        private void LimitDataTableTextLength(DataTable dataTable)
        {
            const int MAX_TEXT_LENGTH = 100; // 限制文本长度为100字符

            foreach (DataRow row in dataTable.Rows)
            {
                foreach (DataColumn column in dataTable.Columns)
                {
                    if (column.DataType == typeof(string) && row[column] != null && row[column] != DBNull.Value)
                    {
                        string text = row[column].ToString();
                        if (text.Length > MAX_TEXT_LENGTH)
                        {
                            row[column] = text.Substring(0, MAX_TEXT_LENGTH) + "...";
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 在禁用皮肤引擎的情况下刷新数据
        /// </summary>
        private void RefreshDataWithoutSkin()
        {
            if (string.IsNullOrEmpty(currentTableName))
                return;

            // 特殊处理推送机型表
            if (currentTableName == "推送机型")
            {
                string errorMessage;
                DataTable dataTable = pushModelManager.GetAllData(out errorMessage);
                if (dataTable != null)
                {
                    LimitDataTableTextLength(dataTable);
                    dgvData.DataSource = dataTable;
                }
                else
                {
                    throw new Exception($"获取推送机型数据失败：{errorMessage}");
                }
                return;
            }

            // 其他表的处理
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                string query = $"SELECT * FROM [{currentTableName}] ORDER BY 编号";
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                    DataTable dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    LimitDataTableTextLength(dataTable);
                    dgvData.DataSource = dataTable;
                }
            }
        }

        private void dgvData_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvData.SelectedRows.Count > 0)
            {
                DataGridViewRow selectedRow = dgvData.SelectedRows[0];
                PopulateInputControls(selectedRow);
            }
        }

        private void PopulateInputControls(DataGridViewRow row)
        {
            for (int i = 0; i < currentTableSchema.Rows.Count; i++)
            {
                string columnName = currentTableSchema.Rows[i]["COLUMN_NAME"].ToString();
                string dataType = currentTableSchema.Rows[i]["DATA_TYPE"].ToString();

                // 跳过自动处理的字段
                if (columnName == "编号" || columnName == "创建人" || columnName == "创建时间" || columnName == "更新时间" || columnName == "更新人")
                    continue;

                // 特殊处理测试类型字段
                if (columnName == "测试类型")
                {
                    Control testTypeControl = inputControls[i];
                    object testTypeValue = row.Cells[columnName].Value;

                    if (testTypeControl is CheckedListBox checkedListBox && testTypeValue != null && testTypeValue != DBNull.Value)
                    {
                        string testTypeValueStr = testTypeValue.ToString();
                        // 清除所有选择
                        for (int j = 0; j < checkedListBox.Items.Count; j++)
                        {
                            checkedListBox.SetItemChecked(j, false);
                        }
                        // 只选中当前行的测试类型（单个类型）
                        int index = checkedListBox.Items.IndexOf(testTypeValueStr);
                        if (index >= 0)
                        {
                            checkedListBox.SetItemChecked(index, true);
                        }
                    }
                    continue;
                }

                Control control = inputControls[i];
                object value = row.Cells[columnName].Value;

                if (value == null || value == DBNull.Value)
                    continue;

                switch (dataType.ToLower())
                {
                    case "bit":
                        if (control is CheckBox checkBox)
                            checkBox.Checked = Convert.ToBoolean(value);
                        break;
                    case "datetime":
                    case "datetime2":
                    case "date":
                        if (control is DateTimePicker dateTimePicker)
                            dateTimePicker.Value = Convert.ToDateTime(value);
                        break;
                    case "int":
                    case "bigint":
                    case "smallint":
                    case "tinyint":
                    case "decimal":
                    case "numeric":
                    case "float":
                    case "real":
                    case "money":
                    case "smallmoney":
                        if (control is NumericUpDown numericUpDown)
                            numericUpDown.Value = Convert.ToDecimal(value);
                        break;
                    default:
                        if (control is TextBox textBox)
                            textBox.Text = value.ToString();
                        break;
                }
            }
        }

        private void ClearInputControls()
        {
            for (int i = 0; i < currentTableSchema.Rows.Count && i < inputControls.Count; i++)
            {
                string columnName = currentTableSchema.Rows[i]["COLUMN_NAME"].ToString();

                // 跳过自动处理的字段
                if (columnName == "编号" || columnName == "创建人" || columnName == "创建时间" || columnName == "更新时间" || columnName == "更新人")
                    continue;

                Control control = inputControls[i];

                // 特殊处理测试类型下拉列表框
                if (columnName == "测试类型" && control is CheckedListBox checkedListBox)
                {
                    for (int j = 0; j < checkedListBox.Items.Count; j++)
                    {
                        checkedListBox.SetItemChecked(j, false);
                    }
                    continue;
                }

                if (control is TextBox textBox)
                    textBox.Clear();
                else if (control is CheckBox checkBox)
                    checkBox.Checked = false;
                else if (control is DateTimePicker dateTimePicker)
                    dateTimePicker.Value = DateTime.Now;
                else if (control is NumericUpDown numericUpDown)
                    numericUpDown.Value = 0;
            }
        }

        private Dictionary<string, object> GetInputValues()
        {
            Dictionary<string, object> values = new Dictionary<string, object>();

            for (int i = 0; i < currentTableSchema.Rows.Count; i++)
            {
                string columnName = currentTableSchema.Rows[i]["COLUMN_NAME"].ToString();
                string dataType = currentTableSchema.Rows[i]["DATA_TYPE"].ToString();

                // 跳过编号字段
                if (columnName == "编号")
                    continue;

                // 为自动处理的字段设置默认值
                if (columnName == "创建人")
                {
                    values[columnName] = "System";
                    continue;
                }

                if (columnName == "创建时间" || columnName == "更新时间" || columnName == "更新人")
                {
                    // 这些字段在SQL中自动处理，不需要在这里设置
                    continue;
                }

                // 特殊处理测试类型下拉列表框
                if (columnName == "测试类型")
                {
                    Control testTypeControl = inputControls[i];
                    if (testTypeControl is CheckedListBox checkedListBox)
                    {
                        List<string> selectedTypes = new List<string>();
                        for (int j = 0; j < checkedListBox.Items.Count; j++)
                        {
                            if (checkedListBox.GetItemChecked(j))
                            {
                                selectedTypes.Add(checkedListBox.Items[j].ToString());
                            }
                        }
                        values[columnName] = selectedTypes.Count > 0 ? (object)string.Join(",", selectedTypes) : (object)DBNull.Value;
                    }
                    continue;
                }

                Control control = inputControls[i];
                object value = null;

                switch (dataType.ToLower())
                {
                    case "bit":
                        if (control is CheckBox checkBox)
                            value = checkBox.Checked;
                        break;
                    case "datetime":
                    case "datetime2":
                    case "date":
                        if (control is DateTimePicker dateTimePicker)
                            value = dateTimePicker.Value;
                        break;
                    case "int":
                    case "bigint":
                    case "smallint":
                    case "tinyint":
                    case "decimal":
                    case "numeric":
                    case "float":
                    case "real":
                    case "money":
                    case "smallmoney":
                        if (control is NumericUpDown numericUpDown)
                            value = numericUpDown.Value;
                        break;
                    default:
                        if (control is TextBox textBox)
                            value = string.IsNullOrEmpty(textBox.Text) ? DBNull.Value : (object)textBox.Text;
                        break;
                }

                values[columnName] = value;
            }

            return values;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentTableName))
            {
                // 错误提示 - Message
                AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                return;
            }

            try
            {
                Dictionary<string, object> values = GetInputValues();

                // 特殊处理特殊机型表
                if (currentTableName == "特殊机型")
                {
                    string testTypes = values["测试类型"]?.ToString();
                    if (string.IsNullOrEmpty(testTypes) || testTypes == DBNull.Value.ToString())
                    {
                        // 错误提示 - Message
                        AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                        return;
                    }

                    string[] selectedTypes = testTypes.Split(',');
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlTransaction transaction = conn.BeginTransaction())
                        {
                            try
                            {
                                foreach (string testType in selectedTypes)
                                {
                                    string insertQuery = @"
                                        INSERT INTO [特殊机型] ([机型前缀], [测试类型], [最大测试次数], [创建时间], [创建人])
                                        VALUES (@机型前缀, @测试类型, @最大测试次数, @CreateTime, @创建人)";

                                    using (SqlCommand cmd = new SqlCommand(insertQuery, conn, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@机型前缀", values["机型前缀"]);
                                        cmd.Parameters.AddWithValue("@测试类型", testType.Trim());
                                        cmd.Parameters.AddWithValue("@最大测试次数", values["最大测试次数"]);
                                        cmd.Parameters.AddWithValue("@CreateTime", DateTime.Now);
                                        cmd.Parameters.AddWithValue("@创建人", "System");
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                transaction.Commit();
                                // 成功提示 - Message
                                AntdUI.Message.success(this, "提示：添加成功！", null, 2);
                                RefreshData();
                                ClearInputControls();
                            }
                            catch
                            {
                                transaction.Rollback();
                                throw;
                            }
                        }
                    }
                }
                else if (currentTableName == "标准工序")
                {
                    string testTypes = values["测试类型"]?.ToString();
                    if (string.IsNullOrEmpty(testTypes) || testTypes == DBNull.Value.ToString())
                    {
                       AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                        return;
                    }

                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlTransaction transaction = conn.BeginTransaction())
                        {
                            try
                            {
                                // 先删除所有现有记录
                                string deleteQuery = "DELETE FROM [标准工序]";
                                using (SqlCommand deleteCmd = new SqlCommand(deleteQuery, conn, transaction))
                                {
                                    deleteCmd.ExecuteNonQuery();
                                }

                                // 插入新的记录
                                string[] selectedTypes = testTypes.Split(',');
                                foreach (string testType in selectedTypes)
                                {
                                    string insertQuery = @"
                                        INSERT INTO [标准工序] ([测试类型], [最大测试次数], [创建时间], [创建人])
                                        VALUES (@测试类型, @最大测试次数, @CreateTime, @创建人)";

                                    using (SqlCommand cmd = new SqlCommand(insertQuery, conn, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@测试类型", testType.Trim());
                                        cmd.Parameters.AddWithValue("@最大测试次数", values["最大测试次数"]);
                                        cmd.Parameters.AddWithValue("@CreateTime", DateTime.Now);
                                        cmd.Parameters.AddWithValue("@创建人", "System");
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                transaction.Commit();
                                // 成功提示 - Message
                                AntdUI.Message.success(this, "提示：添加成功！", null, 2);
                                RefreshData();
                                ClearInputControls();
                            }
                            catch
                            {
                                transaction.Rollback();
                                throw;
                            }
                        }
                    }
                }
                else if (currentTableName == "特殊工序")
                {
                    string testTypes = values["测试类型"]?.ToString();
                    if (string.IsNullOrEmpty(testTypes) || testTypes == DBNull.Value.ToString())
                    {
                       AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                        return;
                    }

                    string processName = values["工序名称"]?.ToString();
                    if (string.IsNullOrEmpty(processName))
                    {
                        MessageBox.Show("请输入工序名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlTransaction transaction = conn.BeginTransaction())
                        {
                            try
                            {
                                string[] selectedTypes = testTypes.Split(',');
                                foreach (string testType in selectedTypes)
                                {
                                    string insertQuery = @"
                                        INSERT INTO [特殊工序] ([工序名称], [测试类型], [最大测试次数], [创建时间], [创建人])
                                        VALUES (@工序名称, @测试类型, @最大测试次数, @CreateTime, @创建人)";

                                    using (SqlCommand cmd = new SqlCommand(insertQuery, conn, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@工序名称", processName);
                                        cmd.Parameters.AddWithValue("@测试类型", testType.Trim());
                                        cmd.Parameters.AddWithValue("@最大测试次数", values["最大测试次数"]);
                                        cmd.Parameters.AddWithValue("@CreateTime", DateTime.Now);
                                        cmd.Parameters.AddWithValue("@创建人", "System");
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                transaction.Commit();
                                // 成功提示 - Message
                                AntdUI.Message.success(this, "提示：添加成功！", null, 2);
                                RefreshData();
                                ClearInputControls();
                            }
                            catch
                            {
                                transaction.Rollback();
                                throw;
                            }
                        }
                    }
                }
                else if (currentTableName == "推送机型")
                {
                    // 推送机型表的专门处理
                    string modelName = values["机型名称"]?.ToString();
                    if (string.IsNullOrWhiteSpace(modelName))
                    {
                        MessageBox.Show("请输入机型名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    string errorMessage;
                    int newId;
                    if (pushModelManager.AddModel(modelName, out errorMessage, out newId))
                    {
                        // 成功提示 - Message
                        AntdUI.Message.success(this, "提示：添加成功！", null, 2);
                        RefreshData();
                        ClearInputControls();
                    }
                    else
                    {
                        // 成功提示 - Message
                        AntdUI.Message.error(this, "提示：添加失败！", null, 2);
                    }
                }
                else
                {
                    // 其他表的正常处理
                    List<string> columns = new List<string>();
                    List<string> parameters = new List<string>();

                    foreach (var kvp in values)
                    {
                        if (kvp.Value != DBNull.Value)
                        {
                            columns.Add($"[{kvp.Key}]");
                            parameters.Add($"@{kvp.Key}");
                        }
                    }

                    columns.Add("[创建时间]");
                    parameters.Add("@CreateTime");

                    string insertQuery = $"INSERT INTO [{currentTableName}] ({string.Join(", ", columns)}) VALUES ({string.Join(", ", parameters)})";

                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand(insertQuery, conn))
                        {
                            foreach (var kvp in values)
                            {
                                if (kvp.Value != DBNull.Value)
                                {
                                    cmd.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value);
                                }
                            }
                            cmd.Parameters.AddWithValue("@CreateTime", DateTime.Now);

                            int result = cmd.ExecuteNonQuery();
                            if (result > 0)
                            {
                                // 成功提示 - Message
                                AntdUI.Message.success(this, "提示：添加成功！", null, 2);
                                RefreshData();
                                ClearInputControls();
                            }
                        }
                    }
                }
            }
            catch (ArgumentOutOfRangeException ex) when (ex.Message.Contains("容量超出了最大容量") || ex.StackTrace.Contains("IrisSkin"))
            {
                // 皮肤引擎错误，尝试禁用皮肤引擎后重试
                try
                {
                    skinEngine1.Active = false;
                    MessageBox.Show("添加操作遇到皮肤引擎问题，已临时禁用皮肤。请重试添加操作。", "皮肤引擎警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                catch (Exception retryEx)
                {
                   AntdUI.Message.error(this, "提示：添加失败！", null, 2);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：添加失败！", null, 2);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentTableName))
            {
                // 错误提示 - Message
                AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                return;
            }

            if (dgvData.SelectedRows.Count == 0)
            {
                // 警告提示 - Message
                AntdUI.Message.warn(this, "警告：请先选择要修改的记录", null, 1);
               
                return;
            }

            try
            {
                Dictionary<string, object> values = GetInputValues();
                DataGridViewRow selectedRow = dgvData.SelectedRows[0];

                // 特殊处理推送机型表
                if (currentTableName == "推送机型")
                {
                    object idValue = selectedRow.Cells["编号"].Value;
                    string modelName = values["机型名称"]?.ToString();

                    if (idValue != null && int.TryParse(idValue.ToString(), out int id))
                    {
                        if (string.IsNullOrWhiteSpace(modelName))
                        {
                            MessageBox.Show("请输入机型名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }

                        string errorMessage;
                        if (pushModelManager.UpdateModel(id, modelName, out errorMessage))
                        {
                             AntdUI.Message.success(this, "提示：修改成功！", null, 2);
                            RefreshData();
                        }
                        else
                        {
                            MessageBox.Show($"修改失败：{errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("无法获取记录编号", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    return;
                }
                // 特殊处理包含测试类型的表
                else if (currentTableName == "特殊机型" || currentTableName == "标准工序" || currentTableName == "特殊工序")
                {
                    // 获取当前记录的主键
                    string primaryKeyColumn = currentTableSchema.Rows[0]["COLUMN_NAME"].ToString();
                    object primaryKeyValue = selectedRow.Cells[primaryKeyColumn].Value;

                    string testTypes = values["测试类型"]?.ToString();
                    if (string.IsNullOrEmpty(testTypes) || testTypes == DBNull.Value.ToString())
                    {
                       AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                        return;
                    }

                    // 只更新当前选中的记录
                    List<string> setClause = new List<string>();
                    foreach (var kvp in values)
                    {
                        if (kvp.Key != primaryKeyColumn && kvp.Value != DBNull.Value)
                        {
                            setClause.Add($"[{kvp.Key}] = @{kvp.Key}");
                        }
                    }
                    setClause.Add("[更新人] = @UpdateUser");
                    setClause.Add("[更新时间] = @UpdateTime");

                    string updateQuery = $"UPDATE [{currentTableName}] SET {string.Join(", ", setClause)} WHERE [{primaryKeyColumn}] = @PrimaryKey";

                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                        {
                            foreach (var kvp in values)
                            {
                                if (kvp.Key != primaryKeyColumn && kvp.Value != DBNull.Value)
                                {
                                    cmd.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value);
                                }
                            }
                            cmd.Parameters.AddWithValue("@UpdateUser", "System");
                            cmd.Parameters.AddWithValue("@UpdateTime", DateTime.Now);
                            cmd.Parameters.AddWithValue("@PrimaryKey", primaryKeyValue);

                            int result = cmd.ExecuteNonQuery();
                            if (result > 0)
                            {
                                 AntdUI.Message.success(this, "提示：修改成功！", null, 2);
                                RefreshData();
                            }
                        }
                    }
                }
                else
                {
                    // 其他表的正常处理
                    string primaryKeyColumn = currentTableSchema.Rows[0]["COLUMN_NAME"].ToString();
                    object primaryKeyValue = selectedRow.Cells[primaryKeyColumn].Value;

                    List<string> setClause = new List<string>();

                    foreach (var kvp in values)
                    {
                        if (kvp.Key != primaryKeyColumn && kvp.Value != DBNull.Value)
                        {
                            setClause.Add($"[{kvp.Key}] = @{kvp.Key}");
                        }
                    }

                    setClause.Add("[更新人] = @UpdateUser");
                    setClause.Add("[更新时间] = @UpdateTime");

                    string updateQuery = $"UPDATE [{currentTableName}] SET {string.Join(", ", setClause)} WHERE [{primaryKeyColumn}] = @PrimaryKey";

                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                        {
                            foreach (var kvp in values)
                            {
                                if (kvp.Key != primaryKeyColumn && kvp.Value != DBNull.Value)
                                {
                                    cmd.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value);
                                }
                            }
                            cmd.Parameters.AddWithValue("@UpdateUser", "System");
                            cmd.Parameters.AddWithValue("@UpdateTime", DateTime.Now);
                            cmd.Parameters.AddWithValue("@PrimaryKey", primaryKeyValue);

                            int result = cmd.ExecuteNonQuery();
                            if (result > 0)
                            {
                                 AntdUI.Message.success(this, "提示：修改成功！", null, 2);
                                RefreshData();
                            }
                        }
                    }
                }
            }
            catch (ArgumentOutOfRangeException ex) when (ex.Message.Contains("容量超出了最大容量") || ex.StackTrace.Contains("IrisSkin"))
            {
                // 皮肤引擎错误，尝试禁用皮肤引擎后重试
                try
                {
                    skinEngine1.Active = false;
                    MessageBox.Show("修改操作遇到皮肤引擎问题，已临时禁用皮肤。请重试修改操作。", "皮肤引擎警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                catch (Exception retryEx)
                {
                    AntdUI.Message.error(this, "提示：修改失败！", null, 2);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：修改失败！", null, 2);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentTableName))
            {
                AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                return;
            }

            if (dgvData.SelectedRows.Count == 0)
            {
                // 信息提示 - Message
                AntdUI.Message.info(this, "提示：请先选择要删除的记录", null, 2);
                
                return;
            }

            //DialogResult result = MessageBox.Show("确定要删除选中的记录吗？", "确认删除",
            //    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            //if (result != DialogResult.Yes)
            //    return;

            // 使用安全删除方法，处理皮肤引擎可能的错误
            SafeDeleteOperation();
        }

        /// <summary>
        /// 安全的删除操作，处理皮肤引擎错误
        /// </summary>
        private void SafeDeleteOperation()
        {
            try
            {
                // 临时禁用皮肤引擎的某些功能来避免错误
                bool skinEngineEnabled = skinEngine1.Active;

                try
                {
                    // 执行删除操作
                    PerformDeleteOperation();
                }
                catch (ArgumentOutOfRangeException ex) when (ex.Message.Contains("容量超出了最大容量") || ex.StackTrace.Contains("IrisSkin"))
                {
                    // 这是皮肤引擎的错误，尝试禁用皮肤引擎后重试
                    try
                    {
                        skinEngine1.Active = false;
                        PerformDeleteOperation();
                        AntdUI.Message.success(this, "提示：删除成功！", null, 2);
                    }
                    catch (Exception retryEx)
                    {
                        AntdUI.Message.error(this, "提示：删除成功！", null, 2);
                    }
                    finally
                    {
                        // 尝试重新启用皮肤引擎
                        try
                        {
                            if (skinEngineEnabled)
                            {
                                skinEngine1.Active = true;
                            }
                        }
                        catch
                        {
                            // 忽略重新启用皮肤引擎的错误
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：删除成功！", null, 2);
            }
        }
        

        /// <summary>
        /// 执行实际的删除操作
        /// </summary>
        private void PerformDeleteOperation()
        {
            DataGridViewRow selectedRow = dgvData.SelectedRows[0];

            // 特殊处理推送机型表
            if (currentTableName == "推送机型")
            {
                object idValue = selectedRow.Cells["编号"].Value;
                if (idValue != null && int.TryParse(idValue.ToString(), out int id))
                {
                    string errorMessage;
                    if (pushModelManager.DeleteModel(id, out errorMessage))
                    {
                        AntdUI.Message.success(this, "提示：删除成功！", null, 2);
                    SafeRefreshData();
                        ClearInputControls();
                    }
                    else
                    {
                        AntdUI.Message.error(this, "提示：删除失败！", null, 2);
                    }
                }
                else
                {
                    MessageBox.Show("无法获取记录编号", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                return;
            }

            // 统一处理其他表的删除操作（删除单条记录）
            string primaryKeyColumn = currentTableSchema.Rows[0]["COLUMN_NAME"].ToString();
            object primaryKeyValue = selectedRow.Cells[primaryKeyColumn].Value;

            string deleteQuery = $"DELETE FROM [{currentTableName}] WHERE [{primaryKeyColumn}] = @PrimaryKey";

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand(deleteQuery, conn))
                {
                    cmd.Parameters.AddWithValue("@PrimaryKey", primaryKeyValue);

                    int deleteResult = cmd.ExecuteNonQuery();
                    if (deleteResult > 0)
                    {
                        // 成功提示 - Message
                        AntdUI.Message.success(this, "提示：删除成功！", null, 2);
                       
                        SafeRefreshData();
                        ClearInputControls();
                    }
                }
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentTableName))
            {
                AntdUI.Message.error(this, "操作失败：请选择一个数据表进行操作", null, 2);
                return;
            }

            try
            {
                Dictionary<string, object> searchValues = GetInputValues();

                // 特殊处理推送机型表
                if (currentTableName == "推送机型")
                {
                    string modelName = searchValues.ContainsKey("机型名称") ? searchValues["机型名称"]?.ToString() : "";

                    if (string.IsNullOrWhiteSpace(modelName))
                    {
                        // 如果没有输入搜索条件，显示所有数据
                        RefreshData();
                        return;
                    }

                    string errorMessage;
                    DataTable dataTable = pushModelManager.SearchByModelName(modelName, out errorMessage);
                    if (dataTable != null)
                    {
                        dgvData.DataSource = dataTable;
                    }
                    else
                    {
                        AntdUI.Message.error(this, "提示：查询失败！", null, 2);
                    }
                    return;
                }

                // 其他表的搜索实现：基于输入控件的值进行搜索
                List<string> whereClause = new List<string>();

                foreach (var kvp in searchValues)
                {
                    if (kvp.Value != DBNull.Value && kvp.Value != null)
                    {
                        string dataType = tableColumns[kvp.Key];
                        if (dataType == "nvarchar" || dataType == "varchar" || dataType == "char" || dataType == "nchar")
                        {
                            whereClause.Add($"[{kvp.Key}] LIKE @{kvp.Key}");
                        }
                        else
                        {
                            whereClause.Add($"[{kvp.Key}] = @{kvp.Key}");
                        }
                    }
                }

                string searchQuery = $"SELECT * FROM [{currentTableName}]";
                if (whereClause.Count > 0)
                {
                    searchQuery += " WHERE " + string.Join(" AND ", whereClause);
                }

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(searchQuery, conn))
                    {
                        foreach (var kvp in searchValues)
                        {
                            if (kvp.Value != DBNull.Value && kvp.Value != null)
                            {
                                string dataType = tableColumns[kvp.Key];
                                if (dataType == "nvarchar" || dataType == "varchar" || dataType == "char" || dataType == "nchar")
                                {
                                    cmd.Parameters.AddWithValue($"@{kvp.Key}", $"%{kvp.Value}%");
                                }
                                else
                                {
                                    cmd.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value);
                                }
                            }
                        }

                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        dgvData.DataSource = dataTable;
                    }
                }
            }
            catch (ArgumentOutOfRangeException ex) when (ex.Message.Contains("容量超出了最大容量") || ex.StackTrace.Contains("IrisSkin"))
            {
                // 皮肤引擎错误，尝试禁用皮肤引擎后重试
                try
                {
                    skinEngine1.Active = false;
                    AntdUI.Message.error(this, "提示：查询失败！", null, 2);
                }
                catch (Exception retryEx)
                {
                    AntdUI.Message.error(this, "提示：查询失败！", null, 2);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "提示：查询失败！", null, 2);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshData();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearInputControls();
        }



        private List<string> GetRequiredColumns()
        {
            List<string> requiredColumns = new List<string>();

            // 根据不同表定义必填字段
            switch (currentTableName)
            {
                case "特殊机型":
                    requiredColumns.AddRange(new[] { "机型前缀", "测试类型", "最大测试次数" });
                    break;
                case "标准工序":
                    requiredColumns.AddRange(new[] { "测试类型", "最大测试次数" });
                    break;
                case "特殊工序":
                    requiredColumns.AddRange(new[] { "工序名称", "测试类型", "最大测试次数" });
                    break;
                case "默认配置":
                    requiredColumns.AddRange(new[] { "默认天数" });
                    break;
                case "推送机型":
                    // 推送机型表的必填字段
                    requiredColumns.AddRange(new[] { "机型名称" });
                    break;
                case "屏蔽机型":
                case "屏蔽工序":
                case "打包配置":
                case "机型过滤":
                    // 这些表的必填字段需要根据实际表结构确定
                    // 暂时返回除了自动字段外的所有字段
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();
                        string query = @"
                            SELECT COLUMN_NAME
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_NAME = @tableName
                            AND COLUMN_NAME NOT IN ('编号', '创建时间', '更新时间', '创建人', '更新人')
                            ORDER BY ORDINAL_POSITION";
                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@tableName", currentTableName);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    requiredColumns.Add(reader["COLUMN_NAME"].ToString());
                                }
                            }
                        }
                    }
                    break;
            }

            return requiredColumns;
        }










    }
}
